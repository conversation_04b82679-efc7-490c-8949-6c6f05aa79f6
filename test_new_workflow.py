"""
Test script cho workflow mới của slide generation service
"""

import asyncio
import logging
from app.services.slide_generation_service import SlideGenerationService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_new_workflow():
    """Test workflow mới 3 bước"""
    try:
        logger.info("🧪 Testing NEW 3-step slide generation workflow...")
        
        # Khởi tạo service
        service = SlideGenerationService()

        # Kiểm tra service availability (sẽ trigger initialization)
        if not service.is_available():
            logger.warning("⚠️ Service not available - some services may not be initialized")
            logger.info("🔧 Testing workflow functions without external dependencies...")
        
        # Test data
        lesson_content = """
        Bài học về Cấu hình electron
        
        Cấu hình electron là cách sắp xếp các electron trong các orbital của nguyên tử. 
        Việc hiểu rõ cấu hình electron giúp chúng ta dự đoán tính chất hóa học của các nguyên tố.
        
        <PERSON><PERSON><PERSON> quy tắc sắp xếp electron:
        1. <PERSON>uy tắc Aufbau: Electron điền vào orbital có mức năng lượng thấp trước
        2. Nguyên lý Pauli: Mỗi orbital chứa tối đa 2 electron với spin ngược chiều
        3. Quy tắc Hund: Electron điền vào các orbital cùng mức năng lượng một cách riêng biệt trước
        
        Ví dụ: Cấu hình electron của Carbon (C): 1s² 2s² 2p²
        """
        
        # Test Bước 1: Xây dựng khung slide
        logger.info("🧪 Testing Step 1: Generate slide framework...")
        framework_result = await service._generate_slide_framework(lesson_content)
        
        if framework_result["success"]:
            logger.info(f"✅ Step 1 passed: {len(framework_result['framework'])} slides generated")
            for i, slide in enumerate(framework_result['framework']):
                logger.info(f"   Slide {i+1}: {slide['title']}")
        else:
            logger.error(f"❌ Step 1 failed: {framework_result['error']}")
            return
        
        # Test Bước 2: Chi tiết hóa slide đầu tiên
        logger.info("🧪 Testing Step 2: Detail slide content...")
        first_slide = framework_result['framework'][0]
        detail_result = await service._detail_slide_content(
            lesson_content, 
            first_slide, 
            None, 
            1
        )
        
        if detail_result["success"]:
            logger.info("✅ Step 2 passed: Slide detailed successfully")
            logger.info(f"   Content preview: {detail_result['detailed_content'][:100]}...")
        else:
            logger.error(f"❌ Step 2 failed: {detail_result['error']}")
            return
        
        # Test Bước 3: Gắn placeholder
        logger.info("🧪 Testing Step 3: Attach placeholders...")
        placeholder_result = await service._attach_placeholders(
            detail_result['detailed_content'],
            1
        )
        
        if placeholder_result["success"]:
            logger.info("✅ Step 3 passed: Placeholders attached successfully")
            logger.info(f"   Content with placeholders: {placeholder_result['content_with_placeholders'][:200]}...")
        else:
            logger.error(f"❌ Step 3 failed: {placeholder_result['error']}")
            return
        
        logger.info("🎉 All 3 steps of NEW workflow passed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")

async def test_utility_functions():
    """Test các hàm utility"""
    try:
        logger.info("🧪 Testing utility functions...")
        
        service = SlideGenerationService()
        
        # Test placeholder detection
        test_text = "TitleName 500"
        result = service._detect_placeholder_type_from_text(test_text)
        if result:
            placeholder_type, max_length = result
            logger.info(f"✅ Placeholder detection: {placeholder_type} with max_length {max_length}")
        else:
            logger.error("❌ Placeholder detection failed")
        
        # Test placeholder content parsing
        test_content = """
        Cấu hình electron #*(LessonName)*#
        Bài học về cách sắp xếp electron #*(LessonDescription)*#
        Ngày: 22-07-2025 #*(CreatedDate)*#
        """
        
        parsed = service._parse_placeholder_content(test_content)
        logger.info(f"✅ Content parsing: {sum(len(v) for v in parsed.values())} items parsed")
        
        logger.info("🎉 Utility functions test completed!")
        
    except Exception as e:
        logger.error(f"❌ Utility test failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_new_workflow())
    asyncio.run(test_utility_functions())

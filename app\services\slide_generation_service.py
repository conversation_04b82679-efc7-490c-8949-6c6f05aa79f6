"""
Slide Generation Service - WORKFLOW MỚI TỐI ƯU HÓA
Xử lý logic sinh nội dung slide từ lesson content theo 3 bước:
1. <PERSON><PERSON><PERSON> dựng khung slide
2. <PERSON> tiết hóa từng slide  
3. Gắn placeholder
"""

import logging
import threading
import re
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.textbook_retrieval_service import TextbookRetrievalService
from app.services.google_slides_service import get_google_slides_service

logger = logging.getLogger(__name__)


class SlideGenerationService:
    """
    Service để sinh nội dung slide từ lesson content theo WORKFLOW MỚI 3 BƯỚC
    Singleton pattern với Lazy Initialization
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Singleton pattern implementation với thread-safe"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SlideGenerationService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Lazy initialization - chỉ khởi tạo một lần"""
        if self._initialized:
            return

        self.llm_service = None
        self.textbook_service = None
        self.slides_service = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        """Ensure services are initialized"""
        if not self._service_initialized:
            logger.info("🔄 SlideGenerationService: First-time initialization triggered")
            self.llm_service = get_llm_service()
            self.textbook_service = TextbookRetrievalService()
            self.slides_service = get_google_slides_service()
            self._service_initialized = True
            logger.info("✅ SlideGenerationService: Initialization completed")

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        self._ensure_service_initialized()
        return (self.llm_service and self.llm_service.is_available() and 
                self.slides_service and self.slides_service.is_available())

    async def generate_slides_from_lesson(
        self,
        lesson_id: str,
        template_id: str,
        config_prompt: Optional[str] = None,
        presentation_title: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tạo slides từ lesson_id và template_id (WORKFLOW MỚI 3 BƯỚC)

        Args:
            lesson_id: ID của bài học
            template_id: ID của Google Slides template
            config_prompt: Prompt cấu hình tùy chỉnh (optional)
            presentation_title: Tiêu đề presentation tùy chỉnh (optional)

        Returns:
            Dict chứa kết quả tạo slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Slide generation service not available"
            }

        try:
            logger.info(f"🚀 Starting NEW 3-step slide generation workflow for lesson {lesson_id}")

            # Bước 1: Lấy nội dung bài học
            lesson_result = await self.textbook_service.get_lesson_content(lesson_id)
            if not lesson_result:
                return {
                    "success": False,
                    "error": f"Could not retrieve lesson content for {lesson_id}"
                }

            lesson_content = lesson_result.get("lesson_content", "")
            if not lesson_content:
                return {
                    "success": False,
                    "error": f"Empty lesson content for {lesson_id}"
                }

            # Bước 2: Copy template và phân tích cấu trúc
            new_title = presentation_title or f"Bài học {lesson_id} - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            copy_and_analyze_result = await self.slides_service.copy_and_analyze_template(template_id, new_title)
            if not copy_and_analyze_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not copy and analyze template: {copy_and_analyze_result['error']}"
                }

            # Lưu template slide IDs ngay sau khi copy
            original_template_slide_ids = [slide.get("slideId") for slide in copy_and_analyze_result.get("slides", [])]
            logger.info(f"📋 Saved original template slide IDs: {original_template_slide_ids}")

            # Bước 3: Thực hiện workflow mới 3 bước
            logger.info("🎯 Executing NEW 3-step slide generation workflow...")
            slides_content = await self._generate_slides_content_new_workflow(
                lesson_content,
                copy_and_analyze_result,
                config_prompt
            )
            if not slides_content["success"]:
                logger.error(f"❌ Failed to generate slides content: {slides_content.get('error')}")
                return slides_content

            logger.info(f"✅ Successfully generated slides content: {len(slides_content.get('slides', []))} slides")

            # Bước 4: Cập nhật nội dung vào presentation
            logger.info("📝 Updating presentation content...")
            update_result = await self.slides_service.update_copied_presentation_content(
                copy_and_analyze_result["copied_presentation_id"],
                slides_content["slides"]
            )
            if not update_result["success"]:
                logger.error(f"❌ Failed to update presentation content: {update_result.get('error')}")
                return {
                    "success": False,
                    "error": f"Could not update presentation content: {update_result['error']}"
                }

            # Bước 5: Xóa template slides gốc
            logger.info("🧹 Cleaning up original template slides...")
            if original_template_slide_ids:
                delete_result = await self.slides_service.delete_all_template_slides(
                    copy_and_analyze_result["copied_presentation_id"],
                    original_template_slide_ids
                )
                logger.info(f"🧹 Template cleanup result: {delete_result}")

            # Bước 6: Hoàn thành
            logger.info("🎉 NEW workflow completed successfully!")
            return {
                "success": True,
                "lesson_id": lesson_id,
                "original_template_id": template_id,
                "presentation_id": copy_and_analyze_result["copied_presentation_id"],
                "presentation_title": copy_and_analyze_result["presentation_title"],
                "web_view_link": copy_and_analyze_result["web_view_link"],
                "slides_created": len(slides_content["slides"]),
                "workflow_info": slides_content.get("workflow_info", {})
            }

        except Exception as e:
            logger.error(f"Error in NEW slide generation workflow: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _generate_slides_content_new_workflow(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Sinh nội dung slides bằng WORKFLOW MỚI 3 BƯỚC

        Args:
            lesson_content: Nội dung bài học
            copied_presentation_info: Thông tin presentation đã copy
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa nội dung slides đã sinh
        """
        try:
            # Phân tích template
            logger.info("🔍 Analyzing template...")
            analyzed_template = self._analyze_template_with_placeholders(copied_presentation_info)

            # Thực hiện workflow 3 bước
            logger.info("🚀 Starting 3-step workflow...")
            workflow_result = await self._execute_slide_generation_workflow(
                lesson_content,
                analyzed_template,
                config_prompt
            )
            
            if not workflow_result["success"]:
                return workflow_result

            return {
                "success": True,
                "slides": workflow_result["slides"],
                "workflow_info": workflow_result.get("workflow_info", {})
            }

        except Exception as e:
            logger.error(f"Error in new workflow: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_slide_generation_workflow(
        self,
        lesson_content: str,
        analyzed_template: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Thực hiện workflow 3 bước: 1) Khung slide, 2) Chi tiết hóa, 3) Gắn placeholder

        Args:
            lesson_content: Nội dung bài học
            analyzed_template: Template đã phân tích
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa kết quả workflow
        """
        try:
            # Kiểm tra input
            if not lesson_content or not lesson_content.strip():
                logger.error("❌ Empty lesson content - stopping workflow")
                return {
                    "success": False,
                    "error": "Empty lesson content - cannot proceed with workflow"
                }

            if not analyzed_template or not analyzed_template.get("slides"):
                logger.error("❌ No template slides available - stopping workflow")
                return {
                    "success": False,
                    "error": "No template slides available - cannot proceed with workflow"
                }

            # BƯỚC 1: Xây dựng khung slide
            logger.info("📋 STEP 1: Building slide framework...")
            framework_result = await self._generate_slide_framework(
                lesson_content,
                config_prompt
            )
            if not framework_result["success"]:
                logger.error(f"❌ Step 1 failed: {framework_result.get('error')}")
                return framework_result

            slide_framework = framework_result["framework"]
            logger.info(f"✅ Step 1 completed: {len(slide_framework)} slides in framework")

            # BƯỚC 2 & 3: Xử lý từng slide tuần tự
            final_slides = []
            workflow_info = {
                "framework_slides": len(slide_framework),
                "processed_slides": 0,
                "failed_slides": 0,
                "skipped_slides": 0
            }

            for i, slide_info in enumerate(slide_framework):
                slide_num = i + 1
                logger.info(f"🔄 Processing slide {slide_num}/{len(slide_framework)}: {slide_info.get('title', 'Untitled')}")

                # BƯỚC 2: Chi tiết hóa slide
                detailed_result = await self._detail_slide_content(
                    lesson_content,
                    slide_info,
                    config_prompt,
                    slide_num
                )

                if not detailed_result["success"]:
                    logger.warning(f"⚠️ Slide {slide_num} detailing failed: {detailed_result.get('error')}")
                    workflow_info["failed_slides"] += 1
                    continue

                # BƯỚC 3: Gắn placeholder
                placeholder_result = await self._attach_placeholders(
                    detailed_result["detailed_content"],
                    slide_num
                )

                if not placeholder_result["success"]:
                    logger.warning(f"⚠️ Slide {slide_num} placeholder attachment failed: {placeholder_result.get('error')}")
                    workflow_info["failed_slides"] += 1
                    continue

                # Map vào template
                mapped_slide = await self._map_slide_to_template(
                    placeholder_result["content_with_placeholders"],
                    analyzed_template,
                    slide_num
                )

                if mapped_slide:
                    final_slides.append(mapped_slide)
                    workflow_info["processed_slides"] += 1
                    logger.info(f"✅ Slide {slide_num} completed successfully")
                else:
                    logger.warning(f"⚠️ Slide {slide_num} template mapping failed")
                    workflow_info["skipped_slides"] += 1

            logger.info(f"🎉 Workflow completed: {workflow_info}")

            return {
                "success": True,
                "slides": final_slides,
                "workflow_info": workflow_info
            }

        except Exception as e:
            logger.error(f"Error in slide generation workflow: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    # === WORKFLOW FUNCTIONS - 3 BƯỚC ===

    async def _generate_slide_framework(
        self,
        lesson_content: str,
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        BƯỚC 1: Xây dựng khung slide từ lesson_content

        Args:
            lesson_content: Nội dung bài học
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa khung slide tổng quát
        """
        try:
            logger.info("📋 Step 1: Generating slide framework...")

            # Tạo prompt cho việc xây dựng khung slide
            framework_prompt = self._create_framework_prompt(lesson_content, config_prompt)

            # Gọi LLM để tạo khung slide
            max_retries = 3
            for attempt in range(max_retries):
                logger.info(f"🤖 Framework generation attempt {attempt + 1}/{max_retries}")

                llm_result = await self.llm_service.generate_content(
                    prompt=framework_prompt,
                    temperature=0.1,
                    max_tokens=10000
                )

                if llm_result["success"] and llm_result.get("text"):
                    framework_text = llm_result["text"].strip()
                    logger.info(f"✅ Framework generated successfully on attempt {attempt + 1}")

                    # Parse framework text thành cấu trúc
                    parsed_framework = self._parse_slide_framework(framework_text)

                    if parsed_framework:
                        logger.info(f"✅ Framework parsed: {len(parsed_framework)} slides")
                        return {
                            "success": True,
                            "framework": parsed_framework,
                            "raw_text": framework_text
                        }
                    else:
                        logger.warning(f"❌ Framework parsing failed on attempt {attempt + 1}")
                        if attempt == max_retries - 1:
                            return {
                                "success": False,
                                "error": "Failed to parse framework after 3 attempts"
                            }
                else:
                    logger.warning(f"❌ LLM call failed on attempt {attempt + 1}: {llm_result.get('error')}")
                    if attempt == max_retries - 1:
                        return {
                            "success": False,
                            "error": f"LLM call failed after {max_retries} attempts"
                        }

                # Wait before retry
                import asyncio
                await asyncio.sleep(1)

            return {
                "success": False,
                "error": "Framework generation failed"
            }

        except Exception as e:
            logger.error(f"Error generating slide framework: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _create_framework_prompt(
        self,
        lesson_content: str,
        config_prompt: Optional[str] = None
    ) -> str:
        """
        Tạo prompt cho việc xây dựng khung slide (Bước 1)

        Args:
            lesson_content: Nội dung bài học
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            str: Prompt cho LLM
        """
        default_config = """
Bạn là chuyên gia thiết kế cấu trúc thuyết trình giáo dục. Nhiệm vụ của bạn là phân tích nội dung bài học và tạo ra KHUNG SLIDE tổng quát.

NGUYÊN TẮC THIẾT KẾ KHUNG:
1. PHÂN TÍCH TOÀN DIỆN - Hiểu rõ nội dung bài học và xác định các chủ đề chính
2. CẤU TRÚC LOGIC - Sắp xếp từ tổng quan đến chi tiết, có thứ tự hợp lý
3. MỤC ĐÍCH RÕ RÀNG - Mỗi slide có mục đích và ý định truyền đạt cụ thể
4. PHÂN CHIA HỢP LÝ - Chia nội dung thành 6-8 slides phù hợp
5. TÍNH LOGIC - Đảm bảo luồng thông tin dễ theo dõi và hiểu
"""

        final_config = config_prompt if config_prompt else default_config

        prompt = f"""
{final_config}

NỘI DUNG BÀI HỌC:
{lesson_content}

NHIỆM VỤ: Tạo KHUNG SLIDE tổng quát cho bài học trên.

YÊU CẦU OUTPUT:
Tạo khung slide theo format sau (CHÍNH XÁC):

SLIDE 1:
TITLE: [Tiêu đề slide]
PURPOSE: [Mục đích của slide này]
MAIN_CONTENT: [Nội dung chính cần truyền đạt]
KNOWLEDGE_FOCUS: [Kiến thức cốt lõi cần nhấn mạnh]

SLIDE 2:
TITLE: [Tiêu đề slide]
PURPOSE: [Mục đích của slide này]
MAIN_CONTENT: [Nội dung chính cần truyền đạt]
KNOWLEDGE_FOCUS: [Kiến thức cốt lõi cần nhấn mạnh]

... (tiếp tục với các slide khác)

QUY TẮC:
- Slide 1 LUÔN là slide giới thiệu bài học
- Các slide tiếp theo chia nội dung theo logic từ tổng quan đến chi tiết
- Mỗi slide có mục đích rõ ràng và không trùng lặp
- Tổng cộng 6-8 slides
- Đảm bảo bao phủ toàn bộ nội dung bài học
- Không bỏ sót kiến thức quan trọng

CHỈ TRẢ VỀ KHUNG SLIDE THEO FORMAT TRÊN, KHÔNG CÓ GIẢI THÍCH THÊM.
"""

        return prompt

    def _parse_slide_framework(self, framework_text: str) -> List[Dict[str, Any]]:
        """
        Parse framework text thành cấu trúc slide

        Args:
            framework_text: Text framework từ LLM

        Returns:
            List[Dict]: Danh sách slide framework
        """
        try:
            import re

            slides = []

            # Pattern để tìm slide blocks
            slide_pattern = r'SLIDE (\d+):\s*\n(?:TITLE:\s*(.+?)\s*\n)?(?:PURPOSE:\s*(.+?)\s*\n)?(?:MAIN_CONTENT:\s*(.+?)\s*\n)?(?:KNOWLEDGE_FOCUS:\s*(.+?)\s*\n)?'

            matches = re.findall(slide_pattern, framework_text, re.DOTALL | re.MULTILINE)

            for match in matches:
                slide_num = int(match[0])
                title = match[1].strip() if match[1] else f"Slide {slide_num}"
                purpose = match[2].strip() if match[2] else ""
                main_content = match[3].strip() if match[3] else ""
                knowledge_focus = match[4].strip() if match[4] else ""

                slide_info = {
                    "slide_number": slide_num,
                    "title": title,
                    "purpose": purpose,
                    "main_content": main_content,
                    "knowledge_focus": knowledge_focus
                }

                slides.append(slide_info)
                logger.debug(f"📋 Parsed slide {slide_num}: {title}")

            # Sort by slide number
            slides.sort(key=lambda x: x["slide_number"])

            logger.info(f"✅ Successfully parsed {len(slides)} slides from framework")
            return slides

        except Exception as e:
            logger.error(f"Error parsing slide framework: {e}")
            return []

    async def _detail_slide_content(
        self,
        lesson_content: str,
        slide_info: Dict[str, Any],
        config_prompt: Optional[str] = None,
        slide_number: int = 1
    ) -> Dict[str, Any]:
        """
        BƯỚC 2: Chi tiết hóa nội dung cho từng slide cụ thể

        Args:
            lesson_content: Nội dung bài học gốc
            slide_info: Thông tin slide từ framework
            config_prompt: Prompt cấu hình tùy chỉnh
            slide_number: Số thứ tự slide

        Returns:
            Dict chứa nội dung slide đã chi tiết hóa
        """
        try:
            logger.info(f"📝 Step 2: Detailing content for slide {slide_number}: {slide_info.get('title')}")

            # Tạo prompt cho việc chi tiết hóa slide
            detail_prompt = self._create_detail_prompt(lesson_content, slide_info, config_prompt)

            # Retry logic: 3 lần
            max_retries = 3
            for attempt in range(max_retries):
                logger.info(f"🤖 Detail generation attempt {attempt + 1}/{max_retries} for slide {slide_number}")

                llm_result = await self.llm_service.generate_content(
                    prompt=detail_prompt,
                    temperature=0.1,
                    max_tokens=8000
                )

                if llm_result["success"] and llm_result.get("text"):
                    detailed_content = llm_result["text"].strip()

                    # Kiểm tra chất lượng content
                    if self._validate_detailed_content(detailed_content, slide_info):
                        logger.info(f"✅ Slide {slide_number} detailed successfully on attempt {attempt + 1}")
                        return {
                            "success": True,
                            "detailed_content": detailed_content,
                            "slide_info": slide_info,
                            "attempt_used": attempt + 1
                        }
                    else:
                        logger.warning(f"❌ Content validation failed for slide {slide_number} on attempt {attempt + 1}")
                        if attempt == max_retries - 1:
                            # Fallback: trả về content gốc nếu 3 lần đều fail
                            logger.warning(f"⚠️ Using fallback content for slide {slide_number}")
                            fallback_content = self._create_fallback_content(slide_info)
                            return {
                                "success": True,
                                "detailed_content": fallback_content,
                                "slide_info": slide_info,
                                "fallback_used": True
                            }
                else:
                    logger.warning(f"❌ LLM call failed for slide {slide_number} on attempt {attempt + 1}: {llm_result.get('error')}")
                    if attempt == max_retries - 1:
                        # Fallback: trả về content gốc nếu 3 lần đều fail
                        logger.warning(f"⚠️ Using fallback content for slide {slide_number} after LLM failures")
                        fallback_content = self._create_fallback_content(slide_info)
                        return {
                            "success": True,
                            "detailed_content": fallback_content,
                            "slide_info": slide_info,
                            "fallback_used": True
                        }

                # Wait before retry
                import asyncio
                await asyncio.sleep(1)

            return {
                "success": False,
                "error": f"Failed to detail slide {slide_number} after {max_retries} attempts"
            }

        except Exception as e:
            logger.error(f"Error detailing slide content: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _create_detail_prompt(
        self,
        lesson_content: str,
        slide_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> str:
        """
        Tạo prompt cho việc chi tiết hóa slide (Bước 2)

        Args:
            lesson_content: Nội dung bài học gốc
            slide_info: Thông tin slide từ framework
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            str: Prompt cho LLM
        """
        default_config = """
Bạn là chuyên gia viết nội dung thuyết trình giáo dục chi tiết. Nhiệm vụ của bạn là chi tiết hóa nội dung cho một slide cụ thể dựa trên khung đã có.

NGUYÊN TẮC CHI TIẾT HÓA:
1. TUÂN THỦ KHUNG - Phát triển nội dung theo đúng mục đích và focus đã định
2. NỘI DUNG ĐẦY ĐỦ - Cung cấp thông tin chi tiết, đầy đủ cho slide
3. PHONG CÁCH PHÙ HỢP - Điều chỉnh thái độ, cách nói phù hợp với đối tượng
4. LOGIC RÕ RÀNG - Trình bày có thứ tự, dễ hiểu
5. CHÍNH XÁC KHOA HỌC - Sử dụng thuật ngữ và ký hiệu chính xác
"""

        final_config = config_prompt if config_prompt else default_config

        slide_title = slide_info.get("title", "")
        slide_purpose = slide_info.get("purpose", "")
        main_content = slide_info.get("main_content", "")
        knowledge_focus = slide_info.get("knowledge_focus", "")

        prompt = f"""
{final_config}

NỘI DUNG BÀI HỌC GỐC:
{lesson_content}

THÔNG TIN SLIDE CẦN CHI TIẾT HÓA:
- Tiêu đề: {slide_title}
- Mục đích: {slide_purpose}
- Nội dung chính: {main_content}
- Kiến thức cốt lõi: {knowledge_focus}

NHIỆM VỤ: Chi tiết hóa nội dung cho slide này.

YÊU CẦU CHI TIẾT HÓA:
1. Phát triển nội dung dựa trên mục đích và focus đã định
2. Trích xuất thông tin chi tiết từ bài học gốc
3. Tạo nội dung đầy đủ, phong phú cho slide
4. Đảm bảo tính chính xác và logic
5. Phù hợp với đối tượng học sinh/sinh viên

OUTPUT YÊU CẦU:
Trả về nội dung chi tiết cho slide theo cấu trúc:

SLIDE_TITLE: {slide_title}
DETAILED_CONTENT:
[Nội dung chi tiết đầy đủ cho slide này, bao gồm:
- Giới thiệu/mở đầu
- Nội dung chính với các điểm quan trọng
- Ví dụ minh họa nếu cần
- Kết luận/tóm tắt nếu phù hợp]

CHỈ TRẢ VỀ NỘI DUNG CHI TIẾT THEO FORMAT TRÊN, KHÔNG CÓ GIẢI THÍCH THÊM.
"""

        return prompt

    def _validate_detailed_content(self, content: str, slide_info: Dict[str, Any]) -> bool:
        """
        Kiểm tra chất lượng nội dung đã chi tiết hóa

        Args:
            content: Nội dung đã chi tiết hóa
            slide_info: Thông tin slide gốc

        Returns:
            bool: True nếu content hợp lệ
        """
        try:
            # Kiểm tra độ dài tối thiểu
            if len(content.strip()) < 50:
                logger.warning("❌ Content too short")
                return False

            # Kiểm tra có chứa tiêu đề slide
            slide_title = slide_info.get("title", "")
            if slide_title and slide_title.lower() not in content.lower():
                logger.warning("❌ Content doesn't contain slide title")
                return False

            # Kiểm tra có cấu trúc SLIDE_TITLE và DETAILED_CONTENT
            if "SLIDE_TITLE:" not in content or "DETAILED_CONTENT:" not in content:
                logger.warning("❌ Content missing required structure")
                return False

            logger.debug("✅ Content validation passed")
            return True

        except Exception as e:
            logger.warning(f"❌ Content validation error: {e}")
            return False

    def _create_fallback_content(self, slide_info: Dict[str, Any]) -> str:
        """
        Tạo nội dung fallback khi LLM fail hoặc content không hợp lệ

        Args:
            slide_info: Thông tin slide từ framework

        Returns:
            str: Nội dung fallback
        """
        slide_title = slide_info.get("title", "Slide")
        main_content = slide_info.get("main_content", "Nội dung slide")
        knowledge_focus = slide_info.get("knowledge_focus", "")

        fallback_content = f"""SLIDE_TITLE: {slide_title}
DETAILED_CONTENT:
{main_content}

{knowledge_focus if knowledge_focus else ""}

[Nội dung này được tạo tự động từ khung slide do LLM không thể chi tiết hóa]"""

        logger.info(f"📝 Created fallback content for slide: {slide_title}")
        return fallback_content

    async def _attach_placeholders(
        self,
        detailed_content: str,
        slide_number: int = 1
    ) -> Dict[str, Any]:
        """
        BƯỚC 3: Gắn placeholder cho nội dung slide đã chi tiết hóa

        Args:
            detailed_content: Nội dung slide đã chi tiết hóa
            slide_number: Số thứ tự slide

        Returns:
            Dict chứa nội dung đã gắn placeholder
        """
        try:
            logger.info(f"🏷️ Step 3: Attaching placeholders for slide {slide_number}")

            # Tạo prompt cho việc gắn placeholder
            placeholder_prompt = self._create_placeholder_prompt(detailed_content)

            # Gọi LLM để gắn placeholder
            llm_result = await self.llm_service.generate_content(
                prompt=placeholder_prompt,
                temperature=0.1,
                max_tokens=8000
            )

            if llm_result["success"] and llm_result.get("text"):
                content_with_placeholders = llm_result["text"].strip()

                # Validate placeholder format
                if self._validate_placeholder_format(content_with_placeholders):
                    logger.info(f"✅ Placeholders attached successfully for slide {slide_number}")
                    return {
                        "success": True,
                        "content_with_placeholders": content_with_placeholders,
                        "slide_number": slide_number
                    }
                else:
                    logger.warning(f"❌ Placeholder validation failed for slide {slide_number}")
                    # Fallback: trả về content gốc với placeholder cơ bản
                    fallback_content = self._create_basic_placeholder_content(detailed_content, slide_number)
                    return {
                        "success": True,
                        "content_with_placeholders": fallback_content,
                        "slide_number": slide_number,
                        "fallback_used": True
                    }
            else:
                logger.warning(f"❌ LLM call failed for placeholder attachment on slide {slide_number}: {llm_result.get('error')}")
                # Fallback: trả về content gốc với placeholder cơ bản
                fallback_content = self._create_basic_placeholder_content(detailed_content, slide_number)
                return {
                    "success": True,
                    "content_with_placeholders": fallback_content,
                    "slide_number": slide_number,
                    "fallback_used": True
                }

        except Exception as e:
            logger.error(f"Error attaching placeholders: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _create_placeholder_prompt(self, detailed_content: str) -> str:
        """
        Tạo prompt cho việc gắn placeholder (Bước 3)

        Args:
            detailed_content: Nội dung slide đã chi tiết hóa

        Returns:
            str: Prompt cho LLM
        """
        prompt = f"""
Bạn là chuyên gia gắn placeholder cho nội dung thuyết trình. Nhiệm vụ của bạn là gắn placeholder annotation cho nội dung slide đã chi tiết hóa.

PLACEHOLDER TYPES HỖ TRỢ:
- LessonName: Tên bài học
- LessonDescription: Mô tả bài học
- CreatedDate: Ngày tạo
- TitleName: Tên tiêu đề chính
- TitleContent: Nội dung tiêu đề chính
- SubtitleName: Tên tiêu đề phụ
- SubtitleContent: Nội dung tiêu đề phụ
- ImageName: Tên hình ảnh
- ImageContent: Mô tả hình ảnh

QUY TẮC GẮN PLACEHOLDER:
1. KHÔNG TỰ Ý THAY ĐỔI quy tắc gắn placeholder hiện tại
2. Sử dụng format: [nội dung] #*(PlaceholderType)*#
3. Gắn placeholder phù hợp với từng phần nội dung
4. Đảm bảo logic phân cấp: TitleName/TitleContent, SubtitleName/SubtitleContent
5. Nhóm nội dung chung cho cùng loại placeholder

NỘI DUNG SLIDE ĐÃ CHI TIẾT HÓA:
{detailed_content}

NHIỆM VỤ: Gắn placeholder annotation cho nội dung trên.

YÊU CẦU OUTPUT:
Trả về nội dung đã gắn placeholder theo format:

[Nội dung với placeholder được gắn chính xác]

VÍ DỤ:
Cấu hình electron #*(LessonName)*#
Bài này giúp hiểu về cấu hình electron trong nguyên tử #*(LessonDescription)*#
Ngày thuyết trình: 15-07-2025 #*(CreatedDate)*#
Khái niệm cơ bản #*(TitleName)*#
Cấu hình electron là cách sắp xếp electron trong các orbital của nguyên tử. Việc hiểu rõ cấu hình này giúp dự đoán tính chất hóa học. #*(TitleContent)*#

CHỈ TRẢ VỀ NỘI DUNG ĐÃ GẮN PLACEHOLDER, KHÔNG CÓ GIẢI THÍCH THÊM.
"""

        return prompt

    def _validate_placeholder_format(self, content: str) -> bool:
        """
        Kiểm tra format placeholder trong content

        Args:
            content: Nội dung đã gắn placeholder

        Returns:
            bool: True nếu format hợp lệ
        """
        try:
            import re

            # Kiểm tra có placeholder annotation
            placeholder_pattern = r'#\*\((\w+)\)\*#'
            matches = re.findall(placeholder_pattern, content)

            if not matches:
                logger.warning("❌ No placeholder annotations found")
                return False

            # Kiểm tra placeholder types hợp lệ
            valid_types = {
                'LessonName', 'LessonDescription', 'CreatedDate',
                'TitleName', 'TitleContent', 'SubtitleName', 'SubtitleContent',
                'ImageName', 'ImageContent'
            }

            for placeholder_type in matches:
                if placeholder_type not in valid_types:
                    logger.warning(f"❌ Invalid placeholder type: {placeholder_type}")
                    return False

            logger.debug(f"✅ Found {len(matches)} valid placeholders: {matches}")
            return True

        except Exception as e:
            logger.warning(f"❌ Placeholder validation error: {e}")
            return False

    def _create_basic_placeholder_content(self, detailed_content: str, slide_number: int) -> str:
        """
        Tạo nội dung với placeholder cơ bản khi LLM fail

        Args:
            detailed_content: Nội dung gốc
            slide_number: Số thứ tự slide

        Returns:
            str: Nội dung với placeholder cơ bản
        """
        try:
            # Parse detailed content để lấy title và content
            lines = detailed_content.split('\n')
            slide_title = ""
            content_lines = []

            in_content_section = False
            for line in lines:
                line = line.strip()
                if line.startswith("SLIDE_TITLE:"):
                    slide_title = line.replace("SLIDE_TITLE:", "").strip()
                elif line.startswith("DETAILED_CONTENT:"):
                    in_content_section = True
                elif in_content_section and line:
                    content_lines.append(line)

            # Tạo content với placeholder cơ bản
            if slide_number == 1:
                # Slide đầu tiên - thông tin bài học
                basic_content = f"""{slide_title} #*(LessonName)*#
{' '.join(content_lines[:2]) if content_lines else 'Nội dung bài học'} #*(LessonDescription)*#
Ngày thuyết trình: {datetime.now().strftime('%d-%m-%Y')} #*(CreatedDate)*#"""
            else:
                # Các slide khác - nội dung chính
                basic_content = f"""{slide_title} #*(TitleName)*#
{' '.join(content_lines) if content_lines else 'Nội dung slide'} #*(TitleContent)*#"""

            logger.info(f"📝 Created basic placeholder content for slide {slide_number}")
            return basic_content

        except Exception as e:
            logger.warning(f"❌ Error creating basic placeholder content: {e}")
            return f"Slide {slide_number} #*(TitleName)*#\nNội dung slide #*(TitleContent)*#"

    # === TEMPLATE ANALYSIS & MAPPING FUNCTIONS ===

    def _analyze_template_with_placeholders(self, copied_presentation_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Phân tích template và thêm placeholder types theo enum yêu cầu

        Args:
            copied_presentation_info: Thông tin presentation đã copy

        Returns:
            Dict chứa template đã phân tích với placeholder types
        """
        try:
            analyzed_slides = []

            for slide in copied_presentation_info.get("slides", []):
                analyzed_elements = []
                placeholder_counts = {}

                for element in slide.get("elements", []):
                    text = element.get("text", "").strip()

                    if text:  # Chỉ xử lý elements có text
                        logger.info(f"🔍 Processing text in slide {slide.get('slideId')}: '{text}'")

                        # Detect placeholder type và max_length từ text
                        placeholder_result = self._detect_placeholder_type_from_text(text)

                        if placeholder_result:  # Chỉ xử lý nếu detect được placeholder
                            placeholder_type, max_length = placeholder_result

                            logger.info(f"✅ Found placeholder: {placeholder_type} <{max_length}>")

                            # Đếm số lượng placeholder types
                            placeholder_counts[placeholder_type] = placeholder_counts.get(placeholder_type, 0) + 1

                            # Tạo analyzed element với thông tin đầy đủ
                            analyzed_element = {
                                "objectId": element.get("objectId"),
                                "text": None,  # LLM sẽ insert nội dung sau
                                "Type": placeholder_type,
                                "max_length": max_length,
                            }

                            analyzed_elements.append(analyzed_element)
                        else:
                            # Bỏ qua text không phải placeholder format
                            logger.info(f"❌ Skipping non-placeholder text: '{text}'")
                            continue

                # Tạo description cho slide dựa trên placeholder counts
                description = self._generate_slide_description(placeholder_counts)

                analyzed_slide = {
                    "slideId": slide.get("slideId"),
                    "description": description,
                    "elements": analyzed_elements,
                    "placeholder_counts": placeholder_counts  # For logic selection
                }

                analyzed_slides.append(analyzed_slide)

            return {
                "slides": analyzed_slides,
                "total_slides": len(analyzed_slides),
                "original_info": copied_presentation_info
            }

        except Exception as e:
            logger.error(f"Error analyzing template with placeholders: {e}")
            return {"slides": [], "total_slides": 0, "original_info": copied_presentation_info}

    def _detect_placeholder_type_from_text(self, text: str) -> Optional[tuple]:
        """
        Detect placeholder type và max_length từ text format "PlaceholderName <max_length>"

        Args:
            text: Text từ element

        Returns:
            tuple: (placeholder_type, max_length) hoặc None nếu không detect được
        """
        try:
            # Tìm pattern "PlaceholderName max_length" (không có dấu < >)
            pattern = r'(\w+)\s+(\d+)'
            match = re.search(pattern, text)

            if match:
                placeholder_name = match.group(1)
                max_length = int(match.group(2))

                # Map placeholder name to enum
                placeholder_type = self._map_to_placeholder_enum(placeholder_name)
                if placeholder_type:  # Chỉ return nếu tìm thấy valid placeholder
                    return placeholder_type, max_length

            return None

        except Exception as e:
            logger.warning(f"Error detecting placeholder type: {e}")
            return None

    def _map_to_placeholder_enum(self, placeholder_name: str) -> Optional[str]:
        """
        Map placeholder name to enum values

        Args:
            placeholder_name: Name from text

        Returns:
            str: Enum placeholder type
        """
        # Mapping dictionary
        mapping = {
            "LessonName": "LessonName",
            "LessonDescription": "LessonDescription",
            "CreatedDate": "CreatedDate",
            "TitleName": "TitleName",
            "TitleContent": "TitleContent",
            "SubtitleName": "SubtitleName",
            "SubtitleContent": "SubtitleContent",
            "BulletItem": "BulletItem",
            "ImageName": "ImageName",
            "ImageContent": "ImageContent"
        }

        return mapping.get(placeholder_name)  # Return None if not found

    def _generate_slide_description(self, placeholder_counts: Dict[str, int]) -> str:
        """
        Generate description for slide based on placeholder counts

        Args:
            placeholder_counts: Dictionary of placeholder type counts

        Returns:
            str: Generated description
        """
        try:
            if not placeholder_counts:
                return "Slide trống"

            descriptions = []
            for placeholder_type, count in placeholder_counts.items():
                if count > 0:
                    if count == 1:
                        descriptions.append(f"1 {placeholder_type}")
                    else:
                        descriptions.append(f"{count} {placeholder_type}")

            if descriptions:
                return f"Slide dành cho {', '.join(descriptions)}"
            else:
                return "Slide trống"

        except Exception as e:
            logger.warning(f"Error generating slide description: {e}")
            return "Slide không xác định"

    async def _map_slide_to_template(
        self,
        content_with_placeholders: str,
        analyzed_template: Dict[str, Any],
        slide_number: int
    ) -> Optional[Dict[str, Any]]:
        """
        Map nội dung đã có placeholder vào template slide phù hợp

        Args:
            content_with_placeholders: Nội dung đã gắn placeholder
            analyzed_template: Template đã phân tích
            slide_number: Số thứ tự slide

        Returns:
            Dict slide đã map hoặc None nếu không thành công
        """
        try:
            logger.info(f"🎯 Mapping slide {slide_number} to template...")

            # Parse placeholder content
            parsed_content = self._parse_placeholder_content(content_with_placeholders)
            if not parsed_content:
                logger.warning(f"❌ Failed to parse placeholder content for slide {slide_number}")
                return None

            # Tìm template phù hợp
            template_slides = analyzed_template.get("slides", [])
            matching_template = self._find_matching_template_for_content(parsed_content, template_slides)

            if not matching_template:
                logger.warning(f"❌ No matching template found for slide {slide_number}")
                return None

            # Tạo slide copy từ template
            mapped_slide = await self._create_mapped_slide_from_template(
                matching_template,
                parsed_content,
                slide_number
            )

            if mapped_slide:
                logger.info(f"✅ Successfully mapped slide {slide_number} to template {matching_template['slideId']}")
                return mapped_slide
            else:
                logger.warning(f"❌ Failed to create mapped slide for slide {slide_number}")
                return None

        except Exception as e:
            logger.error(f"Error mapping slide to template: {e}")
            return None

    def _parse_placeholder_content(self, content: str) -> Dict[str, List[str]]:
        """
        Parse nội dung có placeholder thành dictionary

        Args:
            content: Nội dung có placeholder annotation

        Returns:
            Dict chứa content theo placeholder type
        """
        try:
            import re

            parsed_data = {
                "LessonName": [],
                "LessonDescription": [],
                "CreatedDate": [],
                "TitleName": [],
                "TitleContent": [],
                "SubtitleName": [],
                "SubtitleContent": [],
                "ImageName": [],
                "ImageContent": []
            }

            # Pattern để tìm content với placeholder
            pattern = r'(.+?)\s*#\*\((\w+)\)\*#'
            matches = re.findall(pattern, content)

            for match in matches:
                content_text = match[0].strip()
                placeholder_type = match[1].strip()

                if placeholder_type in parsed_data:
                    parsed_data[placeholder_type].append(content_text)
                    logger.debug(f"📝 Parsed {placeholder_type}: {content_text[:50]}...")

            # Log summary
            total_items = sum(len(items) for items in parsed_data.values())
            logger.info(f"✅ Parsed {total_items} placeholder items")

            return parsed_data

        except Exception as e:
            logger.error(f"Error parsing placeholder content: {e}")
            return {}

    def _find_matching_template_for_content(
        self,
        parsed_content: Dict[str, List[str]],
        template_slides: List[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """
        Tìm template slide phù hợp với parsed content

        Args:
            parsed_content: Content đã parse theo placeholder type
            template_slides: Danh sách template slides

        Returns:
            Template slide phù hợp hoặc None
        """
        try:
            # Tính toán placeholder counts từ parsed content
            content_counts = {}
            for placeholder_type, items in parsed_content.items():
                if items:  # Chỉ đếm những placeholder có content
                    content_counts[placeholder_type] = len(items)

            logger.info(f"🔍 Looking for template matching: {content_counts}")

            # Tìm template có placeholder counts khớp chính xác
            for template in template_slides:
                template_counts = template.get("placeholder_counts", {})

                # So sánh exact match
                if content_counts == template_counts:
                    logger.info(f"✅ Found exact matching template: {template['slideId']}")
                    return template

            # Nếu không có exact match, tìm template có thể chứa được content
            logger.info("🔍 No exact match found, looking for compatible template...")
            for template in template_slides:
                template_counts = template.get("placeholder_counts", {})

                # Kiểm tra template có thể chứa được content không
                can_contain = True
                for placeholder_type, count in content_counts.items():
                    template_count = template_counts.get(placeholder_type, 0)
                    if template_count < count:
                        can_contain = False
                        break

                if can_contain:
                    logger.info(f"✅ Found compatible template: {template['slideId']}")
                    return template

            logger.warning("❌ No compatible template found")
            return None

        except Exception as e:
            logger.error(f"Error finding matching template: {e}")
            return None

    async def _create_mapped_slide_from_template(
        self,
        template_slide: Dict[str, Any],
        parsed_content: Dict[str, List[str]],
        slide_number: int
    ) -> Optional[Dict[str, Any]]:
        """
        Tạo slide đã map từ template và parsed content

        Args:
            template_slide: Template slide
            parsed_content: Content đã parse
            slide_number: Số thứ tự slide

        Returns:
            Slide đã map hoặc None
        """
        try:
            template_slide_id = template_slide.get("slideId")
            template_elements = template_slide.get("elements", [])

            # Tạo slide ID mới
            new_slide_id = f"slide_{slide_number}_{template_slide_id}"

            mapped_elements = []
            content_index = {key: 0 for key in parsed_content.keys()}

            # Map từng element
            for element in template_elements:
                object_id = element.get("objectId")
                placeholder_type = element.get("Type")
                max_length = element.get("max_length", 1000)

                # Lấy content cho placeholder type này
                content_list = parsed_content.get(placeholder_type, [])
                current_index = content_index.get(placeholder_type, 0)

                if current_index < len(content_list):
                    raw_content = content_list[current_index]

                    # Xử lý max_length nếu cần
                    final_content = await self._handle_max_length_content(
                        raw_content,
                        max_length,
                        placeholder_type
                    )

                    mapped_element = {
                        "objectId": object_id,
                        "text": final_content,
                        "Type": placeholder_type,
                        "max_length": max_length
                    }

                    mapped_elements.append(mapped_element)
                    content_index[placeholder_type] += 1

                    logger.debug(f"✅ Mapped {placeholder_type}: {final_content[:50]}...")
                else:
                    logger.warning(f"❌ No content available for {placeholder_type} in element {object_id}")

            if mapped_elements:
                return {
                    "slideId": new_slide_id,
                    "elements": mapped_elements,
                    "action": "create",
                    "baseSlideId": template_slide_id,
                    "slide_order": slide_number,
                    "template_source": template_slide_id
                }
            else:
                logger.warning(f"❌ No elements mapped for slide {slide_number}")
                return None

        except Exception as e:
            logger.error(f"Error creating mapped slide from template: {e}")
            return None

    async def _handle_max_length_content(
        self,
        content: str,
        max_length: int,
        placeholder_type: str
    ) -> str:
        """
        Xử lý content vượt quá max_length bằng cách gọi LLM để viết lại

        Args:
            content: Nội dung gốc
            max_length: Giới hạn độ dài
            placeholder_type: Loại placeholder

        Returns:
            str: Nội dung đã xử lý
        """
        try:
            # Kiểm tra độ dài
            if len(content) <= max_length:
                return content

            logger.info(f"⚠️ Content exceeds max_length ({len(content)} > {max_length}) for {placeholder_type}")
            logger.info("🤖 Requesting LLM to rewrite content with 3 retry attempts...")

            # Thử 3 lần với LLM
            max_retries = 3
            for attempt in range(max_retries):
                logger.info(f"🔄 LLM rewrite attempt {attempt + 1}/{max_retries}")

                # Tạo prompt để LLM viết lại content với độ nghiêm ngặt tăng dần
                strictness_levels = [
                    "súc tích nhưng đầy đủ thông tin",
                    "rất súc tích, chỉ giữ thông tin cốt lõi",
                    "cực kỳ súc tích, chỉ giữ thông tin thiết yếu nhất"
                ]

                rewrite_prompt = f"""
Bạn cần viết lại nội dung sau để phù hợp với giới hạn độ dài NGHIÊM NGẶT, {strictness_levels[attempt]}.

NỘI DUNG GỐC:
{content}

YÊU CẦU NGHIÊM NGẶT:
- Độ dài tối đa: {max_length} ký tự (BẮT BUỘC)
- Lần thử {attempt + 1}/3: {strictness_levels[attempt]}
- Phù hợp với loại placeholder: {placeholder_type}
- Ngôn ngữ rõ ràng, súc tích
- Ký hiệu khoa học chính xác nếu có
- TUYỆT ĐỐI KHÔNG VƯỢT QUÁ {max_length} KÝ TỰ

CHỈ TRẢ VỀ NỘI DUNG ĐÃ VIẾT LẠI, KHÔNG CÓ GIẢI THÍCH THÊM.
"""

                # Gọi LLM để viết lại
                llm_result = await self.llm_service.generate_content(
                    prompt=rewrite_prompt,
                    temperature=0.1,
                    max_tokens=2000
                )

                if llm_result["success"] and llm_result.get("text"):
                    rewritten_content = llm_result["text"].strip()

                    # Kiểm tra độ dài sau khi viết lại
                    if len(rewritten_content) <= max_length:
                        logger.info(f"✅ LLM rewrite successful on attempt {attempt + 1}: {len(rewritten_content)} chars")
                        return rewritten_content
                    else:
                        logger.warning(f"❌ LLM rewrite attempt {attempt + 1} still exceeds limit: {len(rewritten_content)} > {max_length}")
                        if attempt == max_retries - 1:
                            # Fallback: cắt chuỗi và thêm "..."
                            logger.warning(f"⚠️ Using fallback truncation for {placeholder_type}")
                            return content[:max_length-3] + "..."
                else:
                    logger.warning(f"❌ LLM rewrite attempt {attempt + 1} failed: {llm_result.get('error')}")
                    if attempt == max_retries - 1:
                        # Fallback: cắt chuỗi và thêm "..."
                        logger.warning(f"⚠️ Using fallback truncation for {placeholder_type}")
                        return content[:max_length-3] + "..."

                # Wait before retry
                import asyncio
                await asyncio.sleep(1)

            # Fallback cuối cùng
            logger.warning(f"⚠️ All attempts failed, using truncation fallback for {placeholder_type}")
            return content[:max_length-3] + "..."

        except Exception as e:
            logger.error(f"Error handling max length content: {e}")
            # Fallback: cắt chuỗi đơn giản
            return content[:max_length-3] + "..." if len(content) > max_length else content


# === SINGLETON INSTANCE ===
def get_slide_generation_service():
    """Get singleton instance of SlideGenerationService"""
    return SlideGenerationService()

"""
Simple test cho workflow mới - chỉ test các hàm utility
"""

import logging
from app.services.slide_generation_service import SlideGenerationService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_utility_functions():
    """Test các hàm utility không cần LLM"""
    try:
        logger.info("🧪 Testing utility functions...")
        
        service = SlideGenerationService()
        
        # Test 1: Placeholder detection
        logger.info("🧪 Test 1: Placeholder detection...")
        test_cases = [
            "TitleName 500",
            "LessonDescription 1000", 
            "SubtitleContent 300",
            "InvalidPlaceholder 200",
            "Just text without format"
        ]
        
        for test_text in test_cases:
            result = service._detect_placeholder_type_from_text(test_text)
            if result:
                placeholder_type, max_length = result
                logger.info(f"✅ '{test_text}' -> {placeholder_type} ({max_length})")
            else:
                logger.info(f"❌ '{test_text}' -> No placeholder detected")
        
        # Test 2: Placeholder mapping
        logger.info("🧪 Test 2: Placeholder mapping...")
        test_names = ["LessonName", "TitleContent", "InvalidName"]
        for name in test_names:
            mapped = service._map_to_placeholder_enum(name)
            logger.info(f"✅ '{name}' -> {mapped}")
        
        # Test 3: Content parsing
        logger.info("🧪 Test 3: Content parsing...")
        test_content = """
        Cấu hình electron #*(LessonName)*#
        Bài học về cách sắp xếp electron trong nguyên tử #*(LessonDescription)*#
        Ngày thuyết trình: 22-07-2025 #*(CreatedDate)*#
        Khái niệm cơ bản #*(TitleName)*#
        Electron được sắp xếp trong các orbital theo quy tắc nhất định #*(TitleContent)*#
        """
        
        parsed = service._parse_placeholder_content(test_content)
        total_items = sum(len(v) for v in parsed.values())
        logger.info(f"✅ Content parsing: {total_items} items parsed")
        
        for placeholder_type, items in parsed.items():
            if items:
                logger.info(f"   {placeholder_type}: {len(items)} items")
                for item in items:
                    logger.info(f"     - {item[:50]}...")
        
        # Test 4: Slide description generation
        logger.info("🧪 Test 4: Slide description generation...")
        test_counts = [
            {"TitleName": 1, "TitleContent": 1},
            {"LessonName": 1, "LessonDescription": 1, "CreatedDate": 1},
            {"SubtitleName": 2, "SubtitleContent": 2},
            {}
        ]
        
        for counts in test_counts:
            description = service._generate_slide_description(counts)
            logger.info(f"✅ {counts} -> '{description}'")
        
        # Test 5: Framework parsing (mock data)
        logger.info("🧪 Test 5: Framework parsing...")
        mock_framework = """
SLIDE 1:
TITLE: Giới thiệu bài học
PURPOSE: Tạo sự chú ý và giới thiệu chủ đề
MAIN_CONTENT: Cấu hình electron là chủ đề quan trọng
KNOWLEDGE_FOCUS: Hiểu được khái niệm cơ bản

SLIDE 2:
TITLE: Khái niệm cơ bản
PURPOSE: Truyền đạt định nghĩa và khái niệm
MAIN_CONTENT: Electron được sắp xếp trong orbital
KNOWLEDGE_FOCUS: Nắm vững định nghĩa cấu hình electron
"""
        
        parsed_framework = service._parse_slide_framework(mock_framework)
        logger.info(f"✅ Framework parsing: {len(parsed_framework)} slides parsed")
        
        for slide in parsed_framework:
            logger.info(f"   Slide {slide['slide_number']}: {slide['title']}")
            logger.info(f"     Purpose: {slide['purpose']}")
        
        # Test 6: Content validation
        logger.info("🧪 Test 6: Content validation...")
        test_contents = [
            {
                "content": """SLIDE_TITLE: Test Slide
DETAILED_CONTENT:
This is a detailed content for testing validation.""",
                "slide_info": {"title": "Test Slide"}
            },
            {
                "content": "Too short",
                "slide_info": {"title": "Test"}
            },
            {
                "content": "Missing structure content",
                "slide_info": {"title": "Test"}
            }
        ]
        
        for i, test in enumerate(test_contents):
            is_valid = service._validate_detailed_content(test["content"], test["slide_info"])
            logger.info(f"✅ Content {i+1} validation: {is_valid}")
        
        # Test 7: Basic placeholder content creation
        logger.info("🧪 Test 7: Basic placeholder content creation...")
        test_slide_info = {
            "title": "Khái niệm cơ bản",
            "main_content": "Cấu hình electron là cách sắp xếp electron",
            "knowledge_focus": "Hiểu được khái niệm"
        }
        
        basic_content_1 = service._create_basic_placeholder_content(test_slide_info, 1)
        basic_content_2 = service._create_basic_placeholder_content(test_slide_info, 2)
        
        logger.info(f"✅ Basic content slide 1: {basic_content_1[:100]}...")
        logger.info(f"✅ Basic content slide 2: {basic_content_2[:100]}...")
        
        logger.info("🎉 All utility function tests completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_utility_functions()

"""
Test script cho Optimized Slide Generation Workflow
Kiểm tra từng bước của workflow 3 bước
"""

import asyncio
import logging
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.optimized_slide_generation_service import OptimizedSlideGenerationService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OptimizedWorkflowTester:
    """Test class cho optimized slide generation workflow"""
    
    def __init__(self):
        self.service = OptimizedSlideGenerationService()
        self.test_lesson_content = """
Bài học: Cấu hình electron trong nguyên tử

1. Kh<PERSON>i niệm cấu hình electron
Cấu hình electron là cách sắp xếp các electron trong các orbital của nguyên tử. 
<PERSON><PERSON><PERSON> hình này quyết định tính chất hóa học của nguyên tố.

2. <PERSON><PERSON><PERSON> quy tắc sắp xếp electron
- Quy tắc Aufbau: Electron điền vào orbital có mức năng lượng thấp trước
- Nguyên lý Pauli: Mỗi orbital chứa tối đa 2 electron với spin ngược chiều
- Quy tắc Hund: Electron điền vào các orbital cùng mức năng lượng theo cách có spin song song

3. Ví dụ cấu hình electron
- Hydro (H): 1s¹
- Helium (He): 1s²
- Carbon (C): 1s² 2s² 2p²
- Oxygen (O): 1s² 2s² 2p⁴

4. Ứng dụng của cấu hình electron
Cấu hình electron giúp dự đoán tính chất hóa học và khả năng tạo liên kết của các nguyên tố.
"""
    
    async def test_service_availability(self) -> bool:
        """Test service availability"""
        try:
            logger.info("🧪 Testing service availability...")
            is_available = self.service.is_available()
            
            if is_available:
                logger.info("✅ Service is available")
                return True
            else:
                logger.error("❌ Service is not available")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing service availability: {e}")
            return False
    
    async def test_step1_framework(self) -> dict:
        """Test Step 1: Build slide framework"""
        try:
            logger.info("🧪 Testing Step 1: Build slide framework...")
            
            result = await self.service._step1_build_slide_framework(
                self.test_lesson_content,
                "Tạo slide phù hợp với học sinh trung học"
            )
            
            if result["success"]:
                slides_count = len(result.get("slides", []))
                logger.info(f"✅ Step 1 successful: {slides_count} slides in framework")
                
                # Log first slide for inspection
                if result.get("slides"):
                    first_slide = result["slides"][0]
                    logger.info(f"   First slide: {first_slide.get('title', 'No title')}")
                    logger.info(f"   Purpose: {first_slide.get('purpose', 'No purpose')}")
                
                return result
            else:
                logger.error(f"❌ Step 1 failed: {result.get('error')}")
                return result
                
        except Exception as e:
            logger.error(f"❌ Error in Step 1 test: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_step2_detail_single(self, slide_info: dict) -> dict:
        """Test Step 2: Detail single slide"""
        try:
            slide_number = slide_info.get("slide_number", 1)
            logger.info(f"🧪 Testing Step 2: Detail slide {slide_number}...")
            
            result = await self.service._detail_single_slide(
                self.test_lesson_content,
                slide_info,
                "Sử dụng ngôn ngữ đơn giản, dễ hiểu"
            )
            
            if result["success"]:
                slide_data = result.get("slide", {})
                content_length = len(slide_data.get("detailed_content", ""))
                is_fallback = slide_data.get("is_fallback", False)
                
                logger.info(f"✅ Step 2 successful for slide {slide_number}")
                logger.info(f"   Content length: {content_length} characters")
                logger.info(f"   Is fallback: {is_fallback}")
                
                return result
            else:
                logger.error(f"❌ Step 2 failed for slide {slide_number}: {result.get('error')}")
                return result
                
        except Exception as e:
            logger.error(f"❌ Error in Step 2 test: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_step3_placeholder(self, slide_data: dict) -> dict:
        """Test Step 3: Attach placeholders"""
        try:
            slide_number = slide_data.get("slide_number", 1)
            logger.info(f"🧪 Testing Step 3: Attach placeholders to slide {slide_number}...")
            
            result = await self.service._attach_placeholders_to_single_slide(slide_data)
            
            if result["success"]:
                final_slide = result.get("slide", {})
                has_placeholders = final_slide.get("has_placeholders", False)
                is_fallback = final_slide.get("is_fallback", False)
                
                logger.info(f"✅ Step 3 successful for slide {slide_number}")
                logger.info(f"   Has placeholders: {has_placeholders}")
                logger.info(f"   Is fallback: {is_fallback}")
                
                # Show sample placeholder content
                placeholder_content = final_slide.get("placeholder_content", "")
                if "#*(" in placeholder_content:
                    logger.info("   Sample placeholders found in content")
                
                return result
            else:
                logger.error(f"❌ Step 3 failed for slide {slide_number}: {result.get('error')}")
                return result
                
        except Exception as e:
            logger.error(f"❌ Error in Step 3 test: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_full_workflow(self) -> dict:
        """Test complete 3-step workflow"""
        try:
            logger.info("🧪 Testing complete 3-step workflow...")
            
            # Step 1: Framework
            step1_result = await self.test_step1_framework()
            if not step1_result["success"]:
                return {"success": False, "error": "Step 1 failed", "step1": step1_result}
            
            # Step 2: Detail first slide
            if not step1_result.get("slides"):
                return {"success": False, "error": "No slides in framework"}
            
            first_slide = step1_result["slides"][0]
            step2_result = await self.test_step2_detail_single(first_slide)
            if not step2_result["success"]:
                return {"success": False, "error": "Step 2 failed", "step1": step1_result, "step2": step2_result}
            
            # Step 3: Attach placeholders
            slide_data = step2_result.get("slide", {})
            step3_result = await self.test_step3_placeholder(slide_data)
            if not step3_result["success"]:
                return {"success": False, "error": "Step 3 failed", "step1": step1_result, "step2": step2_result, "step3": step3_result}
            
            logger.info("✅ Complete 3-step workflow test successful!")
            
            return {
                "success": True,
                "message": "All 3 steps completed successfully",
                "step1": step1_result,
                "step2": step2_result,
                "step3": step3_result
            }
            
        except Exception as e:
            logger.error(f"❌ Error in full workflow test: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_input_validation(self) -> dict:
        """Test input validation"""
        try:
            logger.info("🧪 Testing input validation...")
            
            # Test empty lesson_id
            result1 = self.service._validate_input("", "template_123")
            assert not result1["success"], "Should fail with empty lesson_id"
            
            # Test empty template_id  
            result2 = self.service._validate_input("lesson_123", "")
            assert not result2["success"], "Should fail with empty template_id"
            
            # Test valid inputs
            result3 = self.service._validate_input("lesson_123", "template_456")
            assert result3["success"], "Should succeed with valid inputs"
            
            logger.info("✅ Input validation tests passed")
            return {"success": True, "message": "Input validation working correctly"}
            
        except Exception as e:
            logger.error(f"❌ Error in input validation test: {e}")
            return {"success": False, "error": str(e)}
    
    async def run_all_tests(self):
        """Run all tests"""
        logger.info("🚀 Starting Optimized Slide Generation Workflow Tests")
        logger.info("=" * 60)
        
        test_results = {}
        
        # Test 1: Service availability
        test_results["service_availability"] = await self.test_service_availability()
        
        if not test_results["service_availability"]:
            logger.error("❌ Service not available, skipping other tests")
            return test_results
        
        # Test 2: Input validation
        test_results["input_validation"] = await self.test_input_validation()
        
        # Test 3: Full workflow
        test_results["full_workflow"] = await self.test_full_workflow()
        
        # Summary
        logger.info("=" * 60)
        logger.info("📊 TEST SUMMARY:")
        
        for test_name, result in test_results.items():
            status = "✅ PASS" if (isinstance(result, bool) and result) or (isinstance(result, dict) and result.get("success")) else "❌ FAIL"
            logger.info(f"   {test_name}: {status}")
        
        return test_results


async def main():
    """Main test function"""
    tester = OptimizedWorkflowTester()
    results = await tester.run_all_tests()
    
    # Exit with appropriate code
    all_passed = all(
        (isinstance(result, bool) and result) or (isinstance(result, dict) and result.get("success"))
        for result in results.values()
    )
    
    if all_passed:
        logger.info("🎉 All tests passed!")
        sys.exit(0)
    else:
        logger.error("❌ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
